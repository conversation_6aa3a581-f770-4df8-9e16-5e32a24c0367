<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div [class.content-blur]="isLoading">
    <!-- Statistics Cards Row -->
    <div class="row mb-4">
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-primary-gradient">
          <div class="modern-stats-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="modern-stats-info">
            <div class="modern-stats-value">{{ getTotalMembersCount() }}</div>
            <div class="modern-stats-label">Toplam Üye</div>
            <div class="modern-stats-subtext">Üyelik biti<PERSON><PERSON> ya<PERSON></div>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-danger-gradient">
          <div class="modern-stats-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="modern-stats-info">
            <div class="modern-stats-value">{{ getUrgentExpiringCount() }}</div>
            <div class="modern-stats-label">Acil Durum</div>
            <div class="modern-stats-subtext">3 gün ve altı</div>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-warning-gradient">
          <div class="modern-stats-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="modern-stats-info">
            <div class="modern-stats-value">{{ getWarningExpiringCount() }}</div>
            <div class="modern-stats-label">Uyarı</div>
            <div class="modern-stats-subtext">4-7 gün arası</div>
          </div>
        </div>
      </div>
      <div class="col-md-3 col-sm-6 mb-3">
        <div class="modern-stats-card bg-info-gradient">
          <div class="modern-stats-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="modern-stats-info">
            <div class="modern-stats-value">{{ getAverageRemainingDays() }}</div>
            <div class="modern-stats-label">Ortalama</div>
            <div class="modern-stats-subtext">Kalan gün sayısı</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Card -->
    <div class="modern-card">
      <div class="modern-card-header">
        <div class="header-title">
          <h5 class="mb-0">
            <i class="fas fa-hourglass-half me-2 text-primary"></i>
            Üyelik Bitişi Yaklaşanlar
          </h5>
          <p class="text-muted mb-0 mt-1">Üyelik süresi yaklaşan üyelerin listesi ve iletişim bilgileri</p>
        </div>
        <div class="header-actions">
          <div class="search-container">
            <div class="modern-search-box">
              <i class="fas fa-search search-icon"></i>
              <input
                type="text"
                class="modern-form-control"
                placeholder="Üye ara..."
                [(ngModel)]="searchText"
                (ngModelChange)="filterMembers()"
              >
              <button *ngIf="searchText" class="clear-search" (click)="clearSearch()">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="modern-card-body">
        <!-- Empty State -->
        <div *ngIf="filteredMembers.length === 0 && !isLoading" class="empty-state">
          <div class="empty-icon">
            <i class="fas fa-calendar-check fa-4x"></i>
          </div>
          <h4>{{ searchText ? 'Arama sonucu bulunamadı' : 'Yaklaşan üyelik bitişi bulunamadı' }}</h4>
          <p class="text-muted">
            {{ searchText ? 'Farklı arama terimleri deneyebilirsiniz.' : 'Şu anda üyelik bitişi yaklaşan üye bulunmamaktadır.' }}
          </p>
          <button *ngIf="searchText" class="modern-btn modern-btn-outline-primary" (click)="clearSearch()">
            <i class="fas fa-times me-1"></i>
            Aramayı Temizle
          </button>
        </div>

        <!-- Members Grid -->
        <div *ngIf="filteredMembers.length > 0" class="members-grid">
          <div class="grid-header">
            <div class="grid-title">
              <span class="member-count">{{ filteredMembers.length }} üye bulundu</span>
              <button class="sort-toggle-btn" (click)="toggleSort()" title="Sıralama Yönünü Değiştir">
                <i class="fas" [ngClass]="sortDirection === 'asc' ? 'fa-sort-amount-down' : 'fa-sort-amount-up'"></i>
                <span class="ms-1">{{ sortDirection === 'asc' ? 'Artan' : 'Azalan' }}</span>
              </button>
            </div>
          </div>

          <div class="row">
            <div class="col-lg-6 col-xl-4 mb-4" *ngFor="let member of filteredMembers; trackBy: trackByMemberId">
              <div class="member-card modern-card h-100" [ngClass]="getMemberCardClass(member.remainingDays)">
                <div class="member-card-header">
                  <div class="member-avatar-section">
                    <div class="member-avatar-large" [style.backgroundColor]="getAvatarColor(member.memberName)">
                      {{ getInitials(member.memberName) }}
                    </div>
                    <div class="member-basic-info">
                      <h6 class="member-name">{{ member.memberName }}</h6>
                      <span class="member-branch">
                        <i class="fas fa-dumbbell me-1"></i>
                        {{ member.branch }}
                      </span>
                    </div>
                  </div>
                  <div class="urgency-indicator" [ngClass]="getUrgencyClass(member.remainingDays)">
                    <i class="fas" [ngClass]="getUrgencyIcon(member.remainingDays)"></i>
                  </div>
                </div>

                <div class="member-card-body">
                  <div class="member-details">
                    <div class="detail-item">
                      <i class="fas fa-phone text-muted me-2"></i>
                      <span class="detail-label">Telefon:</span>
                      <span class="detail-value">{{ member.phoneNumber }}</span>
                    </div>

                    <div class="detail-item highlight">
                      <i class="fas fa-calendar-times text-muted me-2"></i>
                      <span class="detail-label">Kalan Süre:</span>
                      <span class="remaining-days" [ngClass]="getRemainingDaysClass(member.remainingDays)">
                        {{ member.remainingDays }} Gün
                      </span>
                    </div>

                    <div class="status-section">
                      <span class="status-badge" [ngClass]="getStatusClass(member.remainingDays)">
                        <i class="fas" [ngClass]="getStatusIcon(member.remainingDays)" me-1></i>
                        {{ getStatusText(member.remainingDays) }}
                      </span>
                    </div>
                  </div>
                </div>

                <div class="member-card-footer">
                  <button
                    class="modern-btn modern-btn-outline-success w-100"
                    (click)="openWhatsApp(member.phoneNumber)"
                    title="WhatsApp ile iletişime geç"
                  >
                    <i class="fab fa-whatsapp me-2"></i>
                    WhatsApp ile Mesaj Gönder
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
