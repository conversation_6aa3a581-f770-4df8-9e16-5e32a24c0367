/* Member Remaining Day Component Styles */

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Modern Stats Cards */
.modern-stats-card {
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.modern-stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.9;
  z-index: 1;
}

.modern-stats-card > * {
  position: relative;
  z-index: 2;
}

.modern-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.modern-stats-info {
  flex-grow: 1;
}

.modern-stats-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

.modern-stats-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.modern-stats-subtext {
  font-size: 0.875rem;
  opacity: 0.8;
  color: white;
}

/* Background Gradients */
.bg-primary-gradient {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
}

.bg-warning-gradient {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: white;
}

.bg-danger-gradient {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.bg-info-gradient {
  background: linear-gradient(135deg, #0dcaf0 0%, #0097b2 100%);
  color: white;
}

/* Header Styles */
.header-title h5 {
  color: var(--text-primary);
  font-weight: 600;
}

.header-title p {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Modern Search Box */
.search-container {
  position: relative;
}

.modern-search-box {
  position: relative;
  display: flex;
  align-items: center;
  width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: var(--text-muted);
  z-index: 3;
  font-size: 0.875rem;
}

.modern-form-control {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 2.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.modern-form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.clear-search {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  z-index: 3;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background-color: var(--danger-light);
  color: var(--danger);
}

/* Member Avatar */
.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
}

.status-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

.status-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.status-success {
  background-color: var(--success-light);
  color: var(--success);
}

/* Members Grid */
.members-grid {
  margin-top: 1rem;
}

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.grid-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.member-count {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
}

.sort-toggle-btn {
  display: flex;
  align-items: center;
  background: none;
  border: 2px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.sort-toggle-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  background-color: var(--primary-light);
}

/* Member Cards */
.member-card {
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.member-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.member-card-urgent {
  border-color: var(--danger);
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.05) 0%, rgba(200, 35, 51, 0.02) 100%);
}

.member-card-warning {
  border-color: var(--warning);
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(224, 168, 0, 0.02) 100%);
}

.member-card-normal {
  border-color: var(--success);
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(40, 180, 99, 0.02) 100%);
}

.member-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 1.5rem 1rem;
}

.member-avatar-section {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.member-avatar-large {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  margin-right: 1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.member-basic-info {
  flex-grow: 1;
}

.member-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.member-branch {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
}

.urgency-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
}

.urgency-critical {
  background-color: var(--danger);
  color: white;
  animation: pulse 2s infinite;
}

.urgency-warning {
  background-color: var(--warning);
  color: white;
}

.urgency-normal {
  background-color: var(--success);
  color: white;
}

/* Member Card Body */
.member-card-body {
  padding: 0 1.5rem 1rem;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
}

.detail-item.highlight {
  background-color: rgba(67, 97, 238, 0.05);
  border-radius: var(--border-radius-md);
  padding: 0.75rem;
  margin: 0.25rem 0;
}

.detail-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
  margin-left: 0.5rem;
  margin-right: 0.75rem;
  min-width: 70px;
}

.detail-value {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 600;
}

.remaining-days {
  font-size: 1rem;
  font-weight: 700;
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius-pill);
  background-color: var(--bg-secondary);
}

.status-section {
  display: flex;
  justify-content: center;
  margin-top: 0.5rem;
}

/* Member Card Footer */
.member-card-footer {
  padding: 1rem 1.5rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: rgba(0, 0, 0, 0.02);
}

.member-card-footer .modern-btn {
  font-size: 0.875rem;
  padding: 0.75rem 1rem;
  font-weight: 600;
  border-radius: var(--border-radius-lg);
  transition: all 0.3s ease;
}

.member-card-footer .modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Empty State */
.empty-state {
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  margin-bottom: 1.5rem;
  color: var(--text-muted);
  opacity: 0.6;
}

.empty-state h4 {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
}

.empty-state p {
  color: var(--text-muted);
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.member-card {
  animation: slideInUp 0.6s ease-out;
}

.member-card:nth-child(1) { animation-delay: 0.1s; }
.member-card:nth-child(2) { animation-delay: 0.2s; }
.member-card:nth-child(3) { animation-delay: 0.3s; }
.member-card:nth-child(4) { animation-delay: 0.4s; }
.member-card:nth-child(5) { animation-delay: 0.5s; }
.member-card:nth-child(6) { animation-delay: 0.6s; }

/* Dark Mode Support */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(18, 18, 18, 0.8);
}

[data-theme="dark"] .member-card-footer {
  background-color: rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .detail-item.highlight {
  background-color: rgba(67, 97, 238, 0.1);
}

[data-theme="dark"] .member-card-urgent {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(200, 35, 51, 0.05) 100%);
}

[data-theme="dark"] .member-card-warning {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(224, 168, 0, 0.05) 100%);
}

[data-theme="dark"] .member-card-normal {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.1) 0%, rgba(40, 180, 99, 0.05) 100%);
}

/* Responsive Adjustments */
@media (max-width: 1199.98px) {
  .member-card {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 991.98px) {
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
  }

  .modern-search-box {
    width: 100%;
  }

  .grid-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

@media (max-width: 767.98px) {
  .modern-stats-card {
    margin-bottom: 1rem;
  }

  .modern-stats-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }

  .modern-stats-value {
    font-size: 1.5rem;
  }

  .member-card-header {
    padding: 1rem;
  }

  .member-card-body {
    padding: 0 1rem 1rem;
  }

  .member-card-footer {
    padding: 1rem;
  }

  .member-avatar-large {
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }

  .member-name {
    font-size: 1rem;
  }

  .empty-state {
    padding: 3rem 1rem;
  }

  .empty-icon {
    font-size: 3rem !important;
  }
}

@media (max-width: 575.98px) {
  .modern-stats-card {
    padding: 1rem;
  }

  .modern-stats-icon {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
    margin-right: 0.75rem;
  }

  .modern-stats-value {
    font-size: 1.25rem;
  }

  .modern-stats-label {
    font-size: 0.875rem;
  }

  .modern-stats-subtext {
    font-size: 0.75rem;
  }

  .member-card {
    margin-bottom: 1rem;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }

  .detail-label {
    margin-left: 0;
    margin-right: 0;
    min-width: auto;
  }
}
